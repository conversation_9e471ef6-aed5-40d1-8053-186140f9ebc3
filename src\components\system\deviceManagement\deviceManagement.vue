<template>
    <div class="deviceManagement systemDialogStyle">
        <div class="deviceManagement_dataList">
            <div class="dataList_item">
                <div class="dataList_item_text">
                    <div>设备总数</div>
                    <div>{{ equipmentTotal.totalNum }}</div>
                </div>
                <div class="dataList_item_img">
                    <img src="../../../assets/image/managementSystem/icon7.png" alt="" />
                </div>
            </div>
            <div class="dataList_item">
                <div class="dataList_item_text">
                    <div>在线设备数</div>
                    <div>{{ equipmentTotal.onlineNum }}</div>
                </div>
                <div class="dataList_item_img">
                    <img src="../../../assets/image/managementSystem/icon14.png" alt="" />
                </div>
            </div>
            <div class="dataList_item">
                <div class="dataList_item_text">
                    <div>离线设备数</div>
                    <div>{{ equipmentTotal.offlineNum }}</div>
                </div>
                <div class="dataList_item_img">
                    <img src="../../../assets/image/managementSystem/icon9.png" alt="" />
                </div>
            </div>
            <div class="dataList_item">
                <div class="dataList_item_text">
                    <div>报警设备数</div>
                    <div>{{ equipmentTotal.alarmNum }}</div>
                </div>
                <div class="dataList_item_img">
                    <img src="../../../assets/image/managementSystem/icon12.png" alt="" />
                </div>
            </div>
        </div>
        <div class="deviceManagement_con">
            <div class="handleBox">
                <div class="handleBox_item systemFormStyle">
                    <el-select v-model="areaId" clearable placeholder="请选择种植区域">
                        <el-option
                            v-for="item in areaList"
                            :key="item.areaId"
                            :label="item.areaName"
                            :value="item.areaId"
                        ></el-option>
                    </el-select>
                </div>
                <div class="handleBox_item systemFormStyle">
                    <el-select v-model="equipmentTypeId" clearable placeholder="请选择设备类型">
                        <el-option
                            v-for="item in equipmentTypeList"
                            :key="item.value"
                            :label="item.key"
                            :value="item.value"
                        ></el-option>
                    </el-select>
                </div>
                <div class="handleBox_item systemFormStyle">
                    <el-select v-model="type" clearable placeholder="请选择设备状态">
                        <el-option
                            v-for="item in typeData"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        ></el-option>
                    </el-select>
                </div>
                <div class="handleBox_item systemSearchButtonStyle2">
                    <el-button @click="search">查询</el-button>
                </div>
                <div class="handleBox_item systemSearchButtonStyle2">
                    <el-button @click="downLoadList">下载</el-button>
                </div>
                <div class="handleBox_item systemSearchButtonStyle2">
                    <el-button @click="dataRecordIntervalDialog = true">记录设置</el-button>
                </div>
                <div class="handleBox_item systemSearchButtonStyle2" style="width: 110px;">
                    <el-button @click="deviceDynamicDialog = true">设备动态</el-button>
                </div>
                <div class="handleBox_item systemSearchButtonStyle2 addfacility" v-if="showAdd">
                    <el-button icon="el-icon-plus" @click="facilityaddone">添加设备</el-button>
                </div>
            </div>
            <div class="tableBox systemTableStyle">
                <el-table :data="tableData" style="width: 100%" height="100%">
                    <el-table-column type="index" label="序号" align="center" width="90"></el-table-column>
                    <el-table-column align="center" label="类型" width="150">
                        <template slot-scope="scoped">
                            <div>
                                {{ scoped.row.equipmentTypeId | equipmentTypeFormat }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="areaName" align="center" label="区域"></el-table-column>
                    <el-table-column align="center" label="状态" width="150">
                        <template slot-scope="scoped">
                            <div v-if="scoped.row.online && !scoped.row.alarmStatus">
                                在线
                            </div>
                            <div v-if="!scoped.row.online">
                                离线
                            </div>
                            <div v-if="scoped.row.online && scoped.row.alarmStatus">
                                报警
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="position" align="center" label="设备位置"></el-table-column>
                    <el-table-column align="center" label="操作" width="120">
                        <template slot-scope="scoped">
                            <span class="viewData" @click="equipmentDialogOpen(scoped.row)">
                                设备管理
                            </span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="pageBox systemPageChange">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-size="pageSize"
                    background
                    layout="prev, pager, next "
                    :total="total"
                    :page-count="pageCount"
                    :pager-count="9"
                ></el-pagination>
            </div>
        </div>
        <!-- 数据记录间隔弹窗 -->
        <el-dialog
            class="dataRecordIntervalDialog"
            title="数据记录间隔"
            :visible.sync="dataRecordIntervalDialog"
            width="26%"
            center
            :modal-append-to-body="false"
            :show-close="false"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
        >
            <div class="close" @click="recordIntervalClose('Form')">
                <img src="../../../assets/image/managementSystem/close.png" alt="" />
            </div>
            <div class="formList">
                <el-form :model="Form" :rules="rule" ref="Form" label-width="100px" label-position="top">
                    <el-form-item label="种植区域" prop="areaId">
                        <div class="systemFormStyle">
                            <el-select v-model="Form.areaId" placeholder="请选择" popper-class="systemFormStyle">
                                <el-option
                                    v-for="item in areaList"
                                    :key="item.areaId"
                                    :label="item.areaName"
                                    :value="item.areaId"
                                ></el-option>
                            </el-select>
                        </div>
                    </el-form-item>
                    <el-form-item label="气象数据" prop="weather">
                        <div class="tips">
                            <img src="../../../assets/image/centralControlPlatform/warning-tip.png" alt="" />
                            <span>
                                间隔数值为05~1440分钟
                            </span>
                        </div>
                        <div>
                            <div>
                                历史数据记录间隔(分钟)：
                            </div>
                            <div>
                                <el-input v-model="Form.weather" clearable class="systemFormStyle"></el-input>
                            </div>
                        </div>
                    </el-form-item>
                    <el-form-item label="土壤数据" prop="soil">
                        <div class="tips">
                            <img src="../../../assets/image/centralControlPlatform/warning-tip.png" alt="" />
                            <span>
                                间隔数值为05~1440分钟
                            </span>
                        </div>
                        <div>
                            <div>
                                历史数据记录间隔(分钟)：
                            </div>
                            <div>
                                <el-input v-model="Form.soil" clearable class="systemFormStyle"></el-input>
                            </div>
                        </div>
                    </el-form-item>
                    <el-form-item label="虫情" prop="wormCase">
                        <div class="tips" @click="wormCaseAdd">
                            <img src="../../../assets/image/managementSystem/add-icon.png" alt="" />
                        </div>
                        <div class="input_list">
                            <div>
                                历史数据记录间隔(分钟)：
                            </div>
                            <div>
                                <div class="list_item" v-for="(item, index) in Form.wormCase" :key="index">
                                    <el-input
                                        v-model="Form.wormCase[index].hour"
                                        clearable
                                        class="systemFormStyle"
                                    ></el-input>
                                    <span>时</span>
                                    <el-input
                                        v-model="Form.wormCase[index].minute"
                                        clearable
                                        class="systemFormStyle"
                                    ></el-input>
                                    <span>分</span>
                                    <img
                                        @click="wormCaseReduce(index)"
                                        src="../../../assets/image/centralControlPlatform/reduce-icon.png"
                                        alt=""
                                    />
                                </div>
                            </div>
                        </div>
                    </el-form-item>
                    <el-form-item label="苗情球机" prop="cropCase">
                        <div class="tips" @click="cropCaseAdd">
                            <img src="../../../assets/image/managementSystem/add-icon.png" alt="" />
                        </div>
                        <div class="input_list">
                            <div>
                                历史数据记录间隔(分钟)：
                            </div>
                            <div>
                                <div class="list_item" v-for="(item, index) in Form.cropCase" :key="index">
                                    <el-input
                                        v-model="Form.cropCase[index].hour"
                                        clearable
                                        class="systemFormStyle"
                                    ></el-input>
                                    <span>时</span>
                                    <el-input
                                        v-model="Form.cropCase[index].minute"
                                        clearable
                                        class="systemFormStyle"
                                    ></el-input>
                                    <span>分</span>
                                    <img
                                        @click="cropCaseReduce(index)"
                                        src="../../../assets/image/centralControlPlatform/reduce-icon.png"
                                        alt=""
                                    />
                                </div>
                            </div>
                        </div>
                    </el-form-item>
                </el-form>
            </div>
            <div class="btnBox">
                <div class="btnItem systemResetButtonStyle2">
                    <el-button @click="recordIntervalCancel('Form')">取消</el-button>
                </div>
                <div class="btnItem systemSearchButtonStyle2">
                    <el-button @click="recordIntervalSubmit('Form')">确定</el-button>
                </div>
            </div>
        </el-dialog>
        <!-- 设备弹窗  -->
        <el-dialog
            class="equipmentDialog"
            title="设备管理"
            :visible.sync="equipmentDialog"
            width="55%"
            center
            :modal-append-to-body="false"
            :show-close="false"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
        >
            <div class="close" @click="equipmentClose('ruleForm')">
                <img src="../../../assets/image/managementSystem/close.png" alt="" />
            </div>
            <div class="formList">
                <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" label-position="top">
                    <div class="formItem">
                        <!-- <el-form-item label="设备厂家"></el-form-item>
                            <el-input v-model="ruleForm.manufacturer" disabled class="systemFormStyle"></el-input>
                        </el-form-item> -->
                        <!-- <el-form-item label="售后电话">
                            <el-input v-model="ruleForm.telephone" disabled class="systemFormStyle"></el-input>
                        </el-form-item> -->
                        <el-form-item label="设备编号">
                            <el-input v-model="ruleForm.equipmentId" disabled class="systemFormStyle"></el-input>
                        </el-form-item>
                        <el-form-item label="设备运行状态">
                            <el-input v-model="ruleForm.alarmStatusType" disabled class="systemFormStyle"></el-input>
                        </el-form-item>
                        <el-form-item label="设备网络状态">
                            <el-input v-model="ruleForm.onlineType" disabled class="systemFormStyle"></el-input>
                        </el-form-item>
                    </div>
                    <div class="formItem">
                        <el-form-item label="设备运行时间">
                            <el-input v-model="ruleForm.runTime" disabled class="systemFormStyle">
                                <i class=" el-input__icon" slot="suffix">
                                    小时
                                </i>
                            </el-input>
                        </el-form-item>
                        <el-form-item label="设备名称" prop="equipmentName">
                            <el-input v-model="ruleForm.equipmentName" class="systemFormStyle"></el-input>
                        </el-form-item>
                    </div>
                    <!-- <div class="formItem">
                        <el-form-item label="离线判断间隔(分钟)">
                            <el-input v-model="ruleForm.interval" disabled class="systemFormStyle"></el-input>
                        </el-form-item>
                        <el-form-item label="设备名称" prop="equipmentName">
                            <el-input v-model="ruleForm.equipmentName" class="systemFormStyle"></el-input>
                        </el-form-item>
                    </div> -->
                    <div class="formItem">
                        <el-form-item label="经纬度">
                            <el-input v-model="ruleForm.latlng" disabled class="systemFormStyle"></el-input>
                        </el-form-item>
                        <el-form-item label="设备位置信息">
                            <el-input v-model="ruleForm.position"  class="systemFormStyle"></el-input>
                        </el-form-item>
                    </div>
                    <div class="formItem">
                        <el-form-item label="设备归属管理区域">
                            <el-input v-model="ruleForm.areaNames" disabled class="systemFormStyle"></el-input>
                        </el-form-item>
                    </div>
                </el-form>
            </div>
            <div class="btnBox">
                <div class="btnItem systemResetButtonStyle2">
                    <el-button @click="equipmentCancel('ruleForm')">取消</el-button>
                </div>
                <div class="btnItem systemSearchButtonStyle2">
                    <el-button @click="equipmentSubmit('ruleForm')">确定</el-button>
                </div>
            </div>
        </el-dialog>
        <!-- 设备添加弹窗 -->
        <el-dialog
            class="facilityaddDialog"
            title="设备添加"
            :visible.sync="facilityaddDialog"
            v-if="facilityaddDialog"
            width="26%"
            center
            :modal-append-to-body="false"
            :show-close="false"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
        >
            <div class="close" @click="facilityaddDialog = false">
                <img src="../../../assets/image/managementSystem/close.png" alt="" />
            </div>
            <div class="formList">
                <el-form :model="equipmentdata" :rules="equipmentdatarule" ref="equipmentdata" label-width="100px">
                    <el-form-item label="设备型号" prop="areaId">
                        <div class="systemFormStyle">
                            <el-select
                                v-model="equipmentdata.areaId"
                                placeholder="请选择"
                                popper-class="systemFormStyle"
                                clearable
                            >
                                <el-option
                                    v-for="(item, index) in equipmentList"
                                    :key="index"
                                    :label="item"
                                    :value="item"
                                ></el-option>
                            </el-select>
                        </div>
                    </el-form-item>
                </el-form>
                <div class="btnBox">
                    <div class="btnItem systemResetButtonStyle2">
                        <el-button @click="facilityaddDialog = false">取消</el-button>
                    </div>
                    <div class="btnItem systemSearchButtonStyle2">
                        <el-button @click="equipmentdataSubmit">确定</el-button>
                    </div>
                </div>
            </div>
        </el-dialog>
        <!-- 点击设备添加确定后的弹窗 -->
        <el-dialog
            class="dataRecordIntervalDialog"
            title="添加设备"
            :visible.sync="addEquipmentDialog"
            v-if="addEquipmentDialog"
            width="45%"
            center
            :modal-append-to-body="false"
            :show-close="false"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
        >
            <div class="close" @click="addEquipmentDialog = false">
                <img src="../../../assets/image/managementSystem/close.png" alt="" />
            </div>
            <div class="formList">
                <el-form
                    :model="eqInsertPo"
                    :rules="eqInsertPoRule"
                    ref="eqInsertPo"
                    label-width="100px"
                    label-position="top"
                >
                    <div class="basicInformation">基本信息</div>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="设备名称" prop="equipmentName">
                                <div class="systemFormStyle styleinput">
                                    <el-input
                                        placeholder="请输入设备名称"
                                        v-model="eqInsertPo.equipmentName"
                                        clearable
                                    ></el-input>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="序列号" prop="sn">
                                <div class="systemFormStyle styleinput">
                                    <el-input placeholder="请输入设备sn号" v-model="eqInsertPo.sn" clearable></el-input>
                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="设备厂商" prop="manufacturer">
                                <div class="systemFormStyle styleinput">
                                    <el-input
                                        placeholder="请输入设备厂商"
                                        v-model="eqInsertPo.manufacturer"
                                        clearable
                                    ></el-input>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="维护电话" prop="telephone">
                                <div class="systemFormStyle styleinput">
                                    <el-input
                                        placeholder="请输入相关维护电话"
                                        v-model="eqInsertPo.telephone"
                                        clearable
                                    ></el-input>
                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="设备说明" prop="instruction">
                                <div class="systemFormStyle styleinput">
                                    <el-input
                                        class="systemFormStyle"
                                        type="textarea"
                                        clearable
                                        :autosize="{ minRows: 4 }"
                                        placeholder="请输入设备说明"
                                        v-model="eqInsertPo.instruction"
                                    ></el-input>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="设备图片">
                                <div class="uploadBox ">
                                    <el-upload
                                        class="uploadCard125"
                                        action=""
                                        list-type="picture-card"
                                        :file-list="fileList"
                                        :limit="1"
                                        :on-exceed="handleExceed"
                                        :on-change="handleChange"
                                        :auto-upload="false"
                                        :on-remove="handleRemove"
                                    >
                                        <img src="../../../assets/image/centralControlPlatform/upload-add2.png" alt="" />
                                    </el-upload>
                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="14">
                            <el-form-item label="设备位置" prop="position">
                                <div class="systemFormStyle styleinput">
                                    <el-autocomplete
                                        class="inline-input"
                                        v-model="eqInsertPo.position"
                                        :fetch-suggestions="querySearch"
                                        placeholder="请选择位置"
                                        @select="handleSelect"
                                        clearable
                                        style="width:100%;height:48px"
                                    ></el-autocomplete>
                                </div>
                            </el-form-item>
                            <div style="width: 100%; height: 330px;">
                                <zny-map
                                    ref="map1"
                                    style="width: 100%; height: 100%"
                                    :map-options="amapOptions"
                                    :max-zoom="20"
                                    :roadnet="true"
                                    @click="addMarker"
                                ></zny-map>
                            </div>
                        </el-col>
                    </el-row>
                    <div class="basicInformation">指标信息</div>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="主要指标类型" prop="mainTargetType">
                                <div class="systemFormStyle styleinput">
                                    <el-select
                                        v-model="eqInsertPo.mainTargetType"
                                        placeholder="请选择"
                                        popper-class="systemFormStyle"
                                    >
                                        <el-option
                                            v-for="item in findTargetTypeList"
                                            :key="item.areaId"
                                            :label="item.areaName"
                                            :value="item.areaId"
                                        ></el-option>
                                    </el-select>
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="设备支持指标项">
                                <div class="systemFormStyle styleinput">
                                    <el-select
                                        v-model="eqInsertPo.targets"
                                        placeholder="请选择"
                                        multiple
                                        popper-class="systemFormStyle"
                                    >
                                        <el-option
                                            v-for="item in findTargetList"
                                            :key="item.areaId"
                                            :label="item.areaName"
                                            :value="item.areaId"
                                        ></el-option>
                                    </el-select>
                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <div class="basicInformationss" v-if="equipmentdata.areaId == '山大仁科灌排设备'">
                        通道分支
                        <img src="../../../assets/image/managementSystem/add-icon.png" alt="" @click="channelAdd" />
                    </div>
                    <el-row :gutter="20" v-if="equipmentdata.areaId == '山大仁科灌排设备'">
                        <el-col :span="12" v-for="(item, index) in eqInsertPo.irrigationChannelPoList" :key="index">
                            <div class="channel_bigbox">
                                <div class="channel_box">
                                    <el-form-item
                                        :label="`分支${index + 1}`"
                                        :rules="{
                                            required: true,
                                            message: '请选择通道',
                                            trigger: 'blur',
                                        }"
                                    >
                                        <div class="systemFormStyle styleinput">
                                            <el-radio-group v-model="item.mainChannel">
                                                <el-radio :label="true">主通道</el-radio>
                                                <el-radio :label="false">分通道</el-radio>
                                            </el-radio-group>
                                        </div>
                                    </el-form-item>
                                    <el-form-item label="设备名称">
                                        <div class="systemFormStyle styleinput">
                                            <el-input
                                                placeholder="请输入内容"
                                                v-model="item.nodeName"
                                                clearable
                                            ></el-input>
                                        </div>
                                    </el-form-item>
                                    <el-form-item label="设备图片">
                                        <div class="uploadBox " @click.stop="handleAvatarSuccess(index)">
                                            <el-upload
                                                class="uploadCard125"
                                                action=""
                                                list-type="picture-card"
                                                :file-list="fileList"
                                                :limit="1"
                                                :on-exceed="handleExceed"
                                                :on-change="handleChanges"
                                                :auto-upload="false"
                                                :on-remove="handleRemove"
                                            >
                                                <img
                                                    src="../../../assets/image/centralControlPlatform/upload-add2.png"
                                                    alt=""
                                                />
                                            </el-upload>
                                        </div>
                                    </el-form-item>
                                    <img
                                        src="../../../assets/image/managementSystem/shutdown.png"
                                        alt=""
                                        class="shutdown"
                                        v-if="index != 0"
                                        @click="plantToDeler(index, item)"
                                    />
                                </div>
                            </div>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div class="btnBox">
                <div class="btnItem systemResetButtonStyle2">
                    <el-button @click="addEquipmentDialog = false">取消</el-button>
                </div>
                <div class="btnItem systemSearchButtonStyle2">
                    <el-button @click="addEquipmentSubmit">确定</el-button>
                </div>
            </div>
        </el-dialog>
        <!-- 设备动态弹窗 -->
        <el-dialog
            class="deviceDynamicDialog"
            title="设备动态"
            :visible.sync="deviceDynamicDialog"
            width="922px"
            center
            :modal-append-to-body="false"
            :show-close="false"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
        >
            <div class="close" @click="deviceDynamicDialog = false">
                <img src="../../../assets/image/managementSystem/close.png" alt="" />
            </div>
            <div class="deviceDynamicContent">
                <div class="tableBox systemTableStyle">
                    <el-table :data="deviceDynamicData" style="width: 100%" height="100%">
                        <el-table-column prop="deviceName" align="center" label="设备名称" ></el-table-column>
                        <el-table-column prop="operation" align="center" label="操作" ></el-table-column>
                        <el-table-column prop="operationTime" align="center" label="操作时间"></el-table-column>
                    </el-table>
                </div>
                <div class="pageBox systemPageChange">
                    <el-pagination
                        @current-change="handleDynamicCurrentChange"
                        :current-page="dynamicCurrentPage"
                        :page-size="dynamicPageSize"
                        background
                        layout="prev, pager, next, jumper"
                        :total="dynamicTotal"
                        :page-count="dynamicPageCount"
                        :pager-count="15"
                    ></el-pagination>
                </div>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { getBase64 } from '../../../js/base64.js'
import CommonService from '../../../jaxrs/concrete/com.zny.ia.api.CommonService.js'

import EquipmentService from '../../../jaxrs/concrete/com.zny.ia.api.EquipmentService.js'
import HomePageService from '../../../jaxrs/concrete/com.zny.ia.api.ConcreteService.HomePageService.js'
import 指标类型 from '../../../jaxrs/constants/com.zny.ia.constant.deviceDef.指标类型.js'
export default {
    data() {
        return {
            fileList: [],
            radio: '',
            amapOptions: {
                zoom: 12, //地图层级
                center: [117.12665, 36.6584], //中心点
                amapTileUrl: 'https://tiles.zhongnongyun.cn/gcj02&x=[x]&y=[y]&z=[z]',
            },
            // 是否位总管理员
            userRole: localStorage.getItem('userRoles'),
            showAdd:true,//是否显示添加按钮
            equipmentTotal: {}, //数据总览
            areaId: '',
            areaList: [],
            equipmentTypeId: '',
            equipmentTypeList: 指标类型._toArray(),
            type: null,
            typeData: [
                { label: '在线', value: 1 },
                { label: '离线', value: 2 },
                { label: '报警', value: 3 },
            ],
            tableData: [],
            excelData: [],
            // 分页
            pageSize: 20,
            currentPage: 1,
            total: 0,
            pageCount: 1,
            // 数据记录间隔弹窗
            dataRecordIntervalDialog: false,
            Form: {
                areaId: '',
                weather: null, //气象
                soil: null, //土壤
                wormCase: [{ hour: null, minute: null }], //虫情
                cropCase: [{ hour: null, minute: null }], //苗情
            },
            equipmentdatarule: { areaId: [{ required: true, message: '请选择种植区域', trigger: 'blur' }] },
            rule: {
                areaId: [{ required: true, message: '请选择种植区域', trigger: 'change' }],
                weather: [
                    { required: true, message: '请输入气象数据', trigger: 'blur' },
                    {
                        pattern: /(^[1-9]([0-9]+)?$)/,
                        message: '必须为数字值且为整数',
                    },
                ],
                soil: [
                    { required: true, message: '请输入土壤数据', trigger: 'blur' },
                    {
                        pattern: /(^[1-9]([0-9]+)?$)/,
                        message: '必须为数字值且为整数',
                    },
                ],
                wormCase: [{ required: true, message: '请输入虫情数据', trigger: 'blur' }],
                cropCase: [{ required: true, message: '请输入苗情数据', trigger: 'blur' }],
            },
            eqInsertPoRule: {
                equipmentName: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
                sn: [{ required: true, message: '请输入设备sn', trigger: 'blur' }],
                manufacturer: [{ required: true, message: '请输入设备厂商', trigger: 'blur' }],
                telephone: [{ required: true, message: '请输入维护电话', trigger: 'blur' }],
                instruction: [{ required: true, message: '请输入设备说明', trigger: 'blur' }],
                position: [{ required: true, message: '请输入设备位置', trigger: 'blur' }],
                mainTargetType: [{ required: true, message: '请选择主要指标类型', trigger: 'blur' }],
            },
            // 设备弹窗
            title: '',
            equipmentDialog: false,
            ruleForm: {},
            rules: {
                equipmentName: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
            },
            facilityaddDialog: false,
            equipmentList: [],
            equipmentdata: {
                areaId: '',
            },
            addEquipmentDialog: false,
            eqInsertPo: {
                equipmentName: '',
                equipmentType: '',
                instruction: '',
                latitude: '',
                longitude: '',
                mainTargetType: '',
                manufacturer: '',
                picture: null,
                position: null,
                sn: null,
                targets: [],
                telephone: null,
                irrigationChannelPoList: [
                    {
                        mainChannel: null,
                        nodeName: '',
                        picture: null,
                    },
                ],
            },
            findTargetTypeList: [],
            findTargetList: [],
            irrigationChannelPoList: [
                {
                    mainChannel: null,
                    nodeName: '',
                    picture: null,
                },
            ],
            index: null,
            // 设备动态弹窗
            deviceDynamicDialog: false,
            deviceDynamicData: [],
            dynamicCurrentPage: 1,
            dynamicPageSize: 25,
            dynamicTotal: 0,
            dynamicPageCount: 1,
        }
    },
    mounted() {
        this.getList()
        this.getAreaList()
        this.getEquipmentTotal()
        this.getDeviceDynamicList()
        if(this.userRole.indexOf(0) != -1){
            this.showAdd=true
            this.getEquipmentTypeList()
        }else{
            this.showAdd=false
        }
    },
    methods: {
        channelAdd() {
            this.eqInsertPo.irrigationChannelPoList.push({
                mainChannel: null,
                nodeName: '',
                picture: null,
            })
        },
        plantToDeler(index, item) {
            this.eqInsertPo.irrigationChannelPoList.splice(index, 1)
        },
        addEquipmentSubmit() {
            this.$refs.eqInsertPo.validate(valid => {
                if (!valid) return
                if (this.equipmentdata.areaId != '山大仁科灌排设备') {
                    delete this.eqInsertPo.irrigationChannelPoList
                    EquipmentService.insertEquipment(this.eqInsertPo)
                    .then(() => {
                        this.$message({
                            type: 'success',
                            message: '提交成功！',
                        })
                        this.getList()
                        this.addEquipmentDialog = false
                    })
                } else {
                    EquipmentService.insertIrrigationEquipment(this.eqInsertPo)
                    .then(() => {
                        this.$message({
                            type: 'success',
                            message: '提交成功！',
                        })
                        this.getList()
                        this.addEquipmentDialog = false
                    })
                }
            })
        },
        facilityaddone() {
            this.eqInsertPo = {
                equipmentName: '',
                equipmentType: '',
                instruction: '',
                latitude: '',
                longitude: '',
                mainTargetType: '',
                manufacturer: '',
                picture: null,
                position: null,
                sn: null,
                targets: [],
                telephone: null,
                irrigationChannelPoList: [
                    {
                        mainChannel: null,
                        nodeName: '',
                        picture: null,
                    },
                ],
            }
            this.equipmentdata.areaId = ''
            this.facilityaddDialog = true
        },
        equipmentdataSubmit() {
            this.$refs.equipmentdata.validate(valid => {
                if (!valid) return
                this.facilityaddDialog = false
                this.eqInsertPo.equipmentType = this.equipmentdata.areaId
                this.addEquipmentDialog = true
                this.getIndicatorsList()
            })
        },
        // 获取指标列表
        getIndicatorsList() {
            EquipmentService.findTargetTypeList(this.equipmentdata.areaId)
            .then(res => {
                this.findTargetTypeList = res.map(item => {
                    return {
                        areaId: item,
                        areaName: 指标类型._lableOf(item),
                    }
                })
            })
            EquipmentService.findTargetList(this.equipmentdata.areaId)
            .then(res => {
                this.findTargetList = res.map(item => {
                    return {
                        areaId: item,
                        areaName: 指标项._lableOf(item),
                    }
                })
            })
        },
        // 设备总览数据
        getEquipmentTotal() {
            CommonService.findEquipmentTotal().then(res => {
                this.equipmentTotal = res
            })
        },
        getEquipmentTypeList(){
            // 获取平台支持的设备型号列表
            EquipmentService.findEquipmentTypeList().then(res => {
                this.equipmentList = res
            })
        },
        // 获取区域列表
        getAreaList() {
            CommonService.allAreaOfCurrentUserManage().then(res => {
                this.areaList = res
            })
        },
        // 查询
        search() {
            this.currentPage = 1
            this.getList()
        },
        // 获取列表
        getList() {
            let alarmStatus, online
            if (this.type == 1) {
                alarmStatus = false
                online = true
            } else if (this.type == 2) {
                alarmStatus = false
                online = false
            } else if (this.type == 3) {
                alarmStatus = true
                online = true
            }
            let po = {
                num: this.currentPage,
                pageSize: this.pageSize,
                condition: {
                    areaId: this.areaId,
                    equipmentTypeId: this.equipmentTypeId == '' ? null : this.equipmentTypeId,
                    alarmStatus: alarmStatus,
                    online: online,
                },
            }
            EquipmentService.eqSelectList(po).then(res => {
                this.tableData = res.list
                this.pageCount = res.totalPage
                this.total = res.totalNum
            })
        },
        handleSizeChange() {},
        handleCurrentChange(val) {
            this.currentPage = val
            this.getList()
        },
        // 设备动态分页
        handleDynamicCurrentChange(val) {
            this.dynamicCurrentPage = val
            this.getDeviceDynamicList()
        },
        // 获取设备动态列表
        getDeviceDynamicList() {
            // 使用和监测中心一样的接口，获取最近30条设备控制记录
            // 这里使用第一个区域的ID，如果需要可以改为用户选择的区域
            let areaId = null
            if (this.areaList && this.areaList.length > 0) {
                areaId = this.areaList[0].areaId
            }

            if (!areaId) {
                console.warn('没有可用的区域ID')
                this.deviceDynamicData = []
                this.dynamicTotal = 0
                this.dynamicPageCount = 1
                return
            }

            // 调用和监测中心一样的接口
            HomePageService.findEquipmentCtrlStateDynamicTop30List(areaId).then(res => {
                console.log('设备控制记录数据:', res)
                if (res && Array.isArray(res)) {
                    // 将接口返回的数据转换为组件需要的格式
                    const formattedData = res.map(item => ({
                        deviceName: item.name || item.item || '--',
                        operation: item.changeContent || '--',
                        operationTime: item.changeTime || '--'
                    }))

                    // 分页处理
                    const start = (this.dynamicCurrentPage - 1) * this.dynamicPageSize
                    const end = start + this.dynamicPageSize
                    this.deviceDynamicData = formattedData.slice(start, end)
                    this.dynamicTotal = formattedData.length
                    this.dynamicPageCount = Math.ceil(formattedData.length / this.dynamicPageSize)
                } else {
                    this.deviceDynamicData = []
                    this.dynamicTotal = 0
                    this.dynamicPageCount = 1
                }
            }).catch(error => {
                console.error('获取设备控制记录数据失败:', error)
                this.deviceDynamicData = []
                this.dynamicTotal = 0
                this.dynamicPageCount = 1
            })
        },
        // 打开设备弹窗
        equipmentDialogOpen(row) {
            EquipmentService.findEqInfo(row.equipmentId).then(res => {
                if (res.equipmentTypeId != 5) {
                    this.equipmentDialog = true
                }
                // 根据 EqOperationVO 字段结构调整字段映射
                // alarmStatus: 报警状态 false-正常，true-报警
                if (res.alarmStatus) {
                    res.alarmStatusType = '报警'
                } else {
                    res.alarmStatusType = '正常'
                }
                // online: 在线状态 false-离线 true-在线
                if (res.online) {
                    res.onlineType = '在线'
                } else {
                    res.onlineType = '离线'
                }
                // 经纬度组合显示
                res.latlng = res.longitude + ',' + res.latitude

                // 确保数组字段转换为字符串，避免类型错误
                // areaNames 是 List<String> 类型，需要转换为字符串显示
                if (Array.isArray(res.areaNames)) {
                    res.areaNames = res.areaNames.join(', ')
                }

                this.ruleForm = res
            })
        },
        // 设备弹窗关闭
        equipmentClose(formName) {
            this.$refs[formName].resetFields()
            this.equipmentDialog = false
        },
        // 设备弹窗取消
        equipmentCancel(formName) {
            this.$refs[formName].resetFields()
            this.equipmentDialog = false
        },
        // 设备弹窗确定
        equipmentSubmit(formName) {
            const that = this
            that.$refs[formName].validate(valid => {
                if (valid) {
                    EquipmentService.eqInfo(that.ruleForm.equipmentId, that.ruleForm.equipmentName).then(res => {
                        that.$refs[formName].resetFields()
                        that.equipmentDialog = false
                        that.$message({
                            message: '修改成功',
                            type: 'success',
                        })
                    })
                }
            })
        },
        // 虫情列表增加
        wormCaseAdd() {
            this.Form.wormCase.push({ hour: null, minute: null })
        },
        // 虫情减少
        wormCaseReduce(index) {
            this.Form.wormCase.splice(index, 1)
        },
        // 苗情列表增加
        cropCaseAdd() {
            this.Form.cropCase.push({ hour: null, minute: null })
        },
        // 苗情减少
        cropCaseReduce(index) {
            this.Form.cropCase.splice(index, 1)
        },
        // 时间间隔弹窗关闭
        recordIntervalClose(formName) {
            this.$refs[formName].resetFields()
            this.dataRecordIntervalDialog = false
        },
        // 时间间隔弹窗取消
        recordIntervalCancel(formName) {
            this.$refs[formName].resetFields()
            this.dataRecordIntervalDialog = false
        },
        // 时间间隔弹窗确定
        recordIntervalSubmit(formName) {
            const that = this
            that.$refs[formName].validate(valid => {
                if (valid) {
                    let po = this.Form
                    EquipmentService.interRecord(po).then(res => {
                        that.$refs[formName].resetFields()
                        that.dataRecordIntervalDialog = false
                        that.$message({
                            message: '设置成功',
                            type: 'success',
                        })
                    })
                }
            })
        },
        // 下载列表
        downLoadList() {
            EquipmentService.downLoadEpList().then(res => {
                console.log(res)
                res.forEach(e => {
                    e.equipmentType = 指标类型._lableOf(e.equipmentTypeId)
                    let type
                    if (e.online) {
                        if (e.alarmStatus) {
                            type = '报警'
                        } else {
                            type = '在线'
                        }
                    } else {
                        type = '离线'
                    }
                    e.type = type
                })
                this.excelData = res
                this.export2Excel()
            })
        },
        //表格数据写入excel
        export2Excel() {
            var that = this
            require.ensure([], () => {
                const { export_json_to_excel_productPlan } = require('@/excel/Export2Excel.js')
                var tHeader = []
                var filterVal = []
                tHeader = ['类型', '区域', '状态', '设备位置']
                filterVal = ['equipmentType', 'areaName', 'type', 'position']
                const list = that.excelData
                const data = that.formatJson(filterVal, list)
                export_json_to_excel_productPlan(tHeader, data, '设备管理数据表')
            })
        },
        //格式转换，直接复制即可,不需要修改什么
        formatJson(filterVal, jsonData) {
            return jsonData.map(v => filterVal.map(j => v[j]))
        },
        // 根据地址去查询符合条件的位置列表
        querySearch(queryString, cb) {
            this.$refs['map1'].search(queryString, 20).then(result => {
                for (var i = 0; i < result.length; i++) {
                    result[i].value = result[i].name
                }
                cb(result)
            })
        },
        //处理商家宣传图上传的文件
        updatePicProperties(fileList) {
            this.eqInsertPo.picture = fileList
                .filter(f => f._type)
                .map(f => {
                    return { type: f._type, content: f._content }
                })[0]
        },
        // 设备图移除文件
        handleRemove(file, fileList) {
            this.updatePicProperties(fileList)
        },
        // 上传文件个数限制
        handleExceed() {
            this.$message.warning(`当前限制选择 1 个文件`)
        },
        // 设备图上传文件
        handleChange(file, fileList) {
            file._type = file.name.slice(file.name.lastIndexOf('.') + 1)
            getBase64(file.raw).then(res => {
                file._content = res.slice(res.indexOf(',') + 1)
                this.updatePicProperties(fileList)
            })
        },
        handleChanges(file, fileList) {
            file._type = file.name.slice(file.name.lastIndexOf('.') + 1)
            getBase64(file.raw).then(res => {
                file._content = res.slice(res.indexOf(',') + 1)
                this.updatePicPropertiess(fileList)
            })
        },
        handleAvatarSuccess(index) {
            this.index = index
        },
        updatePicPropertiess(fileList) {
            this.eqInsertPo.irrigationChannelPoList[this.index].picture = fileList
                .filter(f => f._type)
                .map(f => {
                    return { type: f._type, content: f._content }
                })[0]
        },
        // 点击添加位置marker
        addMarker(e) {
            this.$refs['map1'].clean()
            let marker = new this.$refs['map1'].Marker({
                position: e.lnglat,
                size: [17, 20],
            })
            this.$refs['map1'].addMarker(marker)
            this.$refs['map1'].center(e.lnglat)
            this.getlocation(e.lnglat)
        },
        // 根据经纬度获取位置及省市县id
        getlocation(lnglat) {
            this.eqInsertPo.longitude = lnglat[0]
            this.eqInsertPo.latitude = lnglat[1]
            this.$refs['map1'].getAddress(lnglat).then(result => {
                this.eqInsertPo.position = result.address
            })
        },
        // 根据查询列表选择位置 以后调用添加marker方法=>添加marker 解析位置及省市县id
        handleSelect(item) {
            this.eqInsertPo.position = item.address
            this.addMarker(item)
        },
    },
    filters: {
        equipmentTypeFormat(value) {
            if (value === null || value === undefined) {
                return ''
            }
            // 使用指标类型常量来格式化显示
            return 指标类型._lableOf(value) || value
        }
    }
}
</script>
<style lang="less">
@import '../../../assets/css/system/deviceManagement.less';
@media screen and (min-width: 1280px) and (max-width: 1680px){
  .deviceManagement{
    .equipmentDialog{
      .el-dialog{
        .el-dialog__body{
          .formList{
            .el-form{
              .el-input{
                width: 240px;
              } 
              .formItem:nth-child(3),
              .formItem:nth-child(4){
                .el-form-item:nth-child(2){
                  .el-input{
                    width: 558px;
                  }
                }
              }
              .formItem:nth-child(5){
                .el-input{
                  width: 877px;
                }
              }
            }
          }
        }
      }
    }
    .dataRecordIntervalDialog{
      .el-dialog{
        width: 29% !important;
      }
    }
  } 
}
@media screen and (max-width: 1280px){
  .deviceManagement{
    .equipmentDialog{
      .el-dialog{
        width: 72% !important;
      }
    }
    .dataRecordIntervalDialog{
      .el-dialog{
        width: 36% !important;
      }
    }
  }
}
</style>

<style scoped lang="less">
.deviceManagement .facilityaddDialog ::v-deep .el-dialog {
    height: 240px;
    margin-top: 32vh !important;
    .formList {
        margin-top: 20px;
    }
    .el-dialog__body .el-form-item__label {
        line-height: 48px !important;
    }
    .systemFormStyle {
        // width: 281px;
        height: 48px;
    }
    .btnBox {
        margin-top: 12px;
        display: flex;
        justify-content: center;
        .btnItem {
            width: 160px;
            height: 40px;
            margin: 0 15px;
        }
    }
}
.channel_bigbox {
    position: relative;
    .channel_box {
        background: rgba(183, 218, 255, 0.1);
        width: 100%;
        padding: 14px;
        margin-top: 24px;
        box-sizing: border-box;
        height: 390px;
        overflow: hidden;
        .shutdown {
            position: absolute;
            right: 0px;
            top: -8px;
            cursor: pointer;
        }
    }
}

::v-deep .el-upload--picture-card {
    height: 98px;
    img {
        margin: auto;
    }
}
.basicInformation {
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #666666;
    opacity: 1;
    margin-top: 43px;
    margin-bottom: 23px;
}
.basicInformationss {
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #666666;
    opacity: 1;
    img {
        vertical-align: middle;
        cursor: pointer;
    }
}
.deviceManagement {
    .addfacility {
        margin-left: auto;
        width: 120px;
    }
}
.styleinput {
    width: 100% !important;
}
// 设备动态弹窗样式已移至全局样式文件
</style>
